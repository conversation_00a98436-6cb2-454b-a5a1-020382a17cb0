apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
  namespace: anti-fraud-platform
  labels:
    app: frontend
    tier: frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
        tier: frontend
    spec:
      containers:
      - name: frontend
        image: frontend:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 3000
        env:
        - name: REACT_APP_API_URL
          value: "http://api-gateway:8080"
        - name: REACT_APP_FIREBASE_API_KEY
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: REACT_APP_FIREBASE_API_KEY
              optional: true
        - name: REACT_APP_FIREBASE_AUTH_DOMAIN
          value: "factcheck-1d6e8.firebaseapp.com"
        - name: REACT_APP_FIREBASE_PROJECT_ID
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: FIREBASE_PROJECT_ID
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: frontend
  namespace: anti-fraud-platform
  labels:
    app: frontend
spec:
  selector:
    app: frontend
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
  type: LoadBalancer
