const express = require('express');
const router = express.Router();
const { exec } = require('child_process');
const path = require('path');
const fs = require('fs-extra');
const { v4: uuidv4 } = require('uuid');

// Test endpoint
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: 'CriminalIP Service is working!',
    timestamp: new Date().toISOString()
  });
});

// Check if nmap is available
router.get('/check-nmap', (req, res) => {
  exec('nmap --version', (error, stdout, stderr) => {
    if (error) {
      return res.json({
        success: false,
        nmapAvailable: false,
        error: 'Nmap not found. Please install nmap.',
        installInstructions: {
          windows: 'Download from https://nmap.org/download.html',
          linux: 'sudo apt-get install nmap',
          mac: 'brew install nmap'
        }
      });
    }
    
    res.json({
      success: true,
      nmapAvailable: true,
      version: stdout.trim(),
      message: 'Nmap is available'
    });
  });
});

// Analyze IP using CriminalIP NSE script
router.post('/analyze-ip', async (req, res) => {
  try {
    const { ip, apiKey } = req.body;
    
    if (!ip) {
      return res.status(400).json({
        success: false,
        error: 'IP address is required',
        code: 'MISSING_IP'
      });
    }
    
    // Validate IP format
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    if (!ipRegex.test(ip)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid IP address format',
        code: 'INVALID_IP'
      });
    }
    
    // Use API key from request or environment
    const criminalipApiKey = apiKey || process.env.CRIMINALIP_API_KEY || 'demo-key';
    
    // Path to NSE script
    const nseScriptPath = path.join(__dirname, '../../criminalip-api.nse');
    
    // Check if NSE script exists
    if (!await fs.pathExists(nseScriptPath)) {
      return res.status(500).json({
        success: false,
        error: 'CriminalIP NSE script not found',
        code: 'SCRIPT_NOT_FOUND'
      });
    }
    
    // Create unique output file
    const outputFile = path.join(__dirname, `../../temp/criminalip-${uuidv4()}.csv`);
    await fs.ensureDir(path.dirname(outputFile));
    
    // Build nmap command
    const nmapCommand = `nmap --script ${nseScriptPath} --script-args 'criminalip-api.target=${ip},criminalip-api.apikey=${criminalipApiKey},criminalip-api.filename=${outputFile}'`;
    
    console.log(`Executing: ${nmapCommand.replace(criminalipApiKey, '***')}`);
    
    // Execute nmap command
    exec(nmapCommand, { timeout: 30000 }, async (error, stdout, stderr) => {
      try {
        let csvData = null;
        
        // Try to read CSV output if file exists
        if (await fs.pathExists(outputFile)) {
          csvData = await fs.readFile(outputFile, 'utf8');
          // Clean up temp file
          await fs.remove(outputFile);
        }
        
        if (error) {
          console.error('Nmap execution error:', error);
          return res.status(500).json({
            success: false,
            error: 'Failed to execute CriminalIP analysis',
            code: 'EXECUTION_ERROR',
            details: error.message,
            stdout: stdout || null,
            stderr: stderr || null
          });
        }
        
        // Parse nmap output
        const result = parseNmapOutput(stdout, csvData);
        
        res.json({
          success: true,
          ip,
          result,
          rawOutput: stdout,
          csvData,
          analyzedAt: new Date().toISOString()
        });
        
      } catch (parseError) {
        console.error('Parse error:', parseError);
        res.status(500).json({
          success: false,
          error: 'Failed to parse analysis results',
          code: 'PARSE_ERROR',
          details: parseError.message
        });
      }
    });
    
  } catch (error) {
    console.error('Analysis error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR',
      details: error.message
    });
  }
});

// Parse nmap output function
function parseNmapOutput(stdout, csvData) {
  const result = {
    hostname: null,
    tags: [],
    categories: [],
    asName: null,
    country: null,
    city: null,
    scores: {
      inbound: null,
      outbound: null
    },
    ports: [],
    summary: null
  };
  
  if (!stdout) {
    return result;
  }
  
  try {
    // Extract information from stdout
    const lines = stdout.split('\n');
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Extract hostname
      if (line.includes('Hostname:')) {
        const hostnameMatch = line.match(/Hostname:\s*([^)]+)/);
        if (hostnameMatch) {
          result.hostname = hostnameMatch[1].trim();
        }
      }
      
      // Extract tags
      if (line.includes('Tag:')) {
        const tagMatch = line.match(/Tag:\s*(.+)/);
        if (tagMatch) {
          result.tags = tagMatch[1].split(',').map(tag => tag.trim());
        }
      }
      
      // Extract categories
      if (line.includes('Category:')) {
        const categoryMatch = line.match(/Category:\s*(.+)/);
        if (categoryMatch) {
          result.categories = categoryMatch[1].split(',').map(cat => cat.trim());
        }
      }
      
      // Extract AS Name
      if (line.includes('As_Name:')) {
        const asMatch = line.match(/As_Name:\s*(.+)/);
        if (asMatch) {
          result.asName = asMatch[1].trim();
        }
      }
      
      // Extract Country and City
      if (line.includes('Country:')) {
        const countryMatch = line.match(/Country:\s*([^(]+)\(City:\s*([^)]+)\)/);
        if (countryMatch) {
          result.country = countryMatch[1].trim();
          result.city = countryMatch[2].trim();
        }
      }
      
      // Extract scores
      if (line.includes('Inbound:') && line.includes('Outbound:')) {
        const scoreMatch = line.match(/Inbound:\s*(\w+).*Outbound:\s*(\w+)/);
        if (scoreMatch) {
          result.scores.inbound = scoreMatch[1];
          result.scores.outbound = scoreMatch[2];
        }
      }
    }
    
    // Parse CSV data if available
    if (csvData) {
      const csvLines = csvData.split('\n');
      if (csvLines.length > 1) {
        const dataLine = csvLines[1].split(',');
        if (dataLine.length >= 7) {
          result.summary = {
            ip: dataLine[0],
            hostname: dataLine[1],
            asName: dataLine[2],
            country: dataLine[3],
            city: dataLine[4],
            inboundScore: dataLine[5],
            outboundScore: dataLine[6]
          };
        }
      }
    }
    
  } catch (parseError) {
    console.error('Parse error:', parseError);
  }
  
  return result;
}

// Mock analysis for testing when nmap is not available
router.post('/mock-analyze', (req, res) => {
  const { ip } = req.body;
  
  if (!ip) {
    return res.status(400).json({
      success: false,
      error: 'IP address is required'
    });
  }
  
  // Mock response
  const mockResult = {
    hostname: `host-${ip.replace(/\./g, '-')}.example.com`,
    tags: ['hosting', 'cloud'],
    categories: ['Safe'],
    asName: 'Example AS',
    country: 'US',
    city: 'San Francisco',
    scores: {
      inbound: 'Safe',
      outbound: 'Safe'
    },
    ports: [
      { port: 80, socket: 'tcp', product: 'nginx', version: '1.18.0' },
      { port: 443, socket: 'tcp', product: 'nginx', version: '1.18.0' }
    ],
    summary: {
      ip,
      hostname: `host-${ip.replace(/\./g, '-')}.example.com`,
      asName: 'Example AS',
      country: 'US',
      city: 'San Francisco',
      inboundScore: 'Safe',
      outboundScore: 'Safe'
    }
  };
  
  res.json({
    success: true,
    ip,
    result: mockResult,
    mock: true,
    analyzedAt: new Date().toISOString()
  });
});

module.exports = router;
