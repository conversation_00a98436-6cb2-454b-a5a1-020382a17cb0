/* Z-index Management - Enterprise Level */
:root {
  /* Base layers */
  --z-index-base: 0;
  --z-index-content: 10;
  --z-index-sticky: 100;

  /* Interactive elements */
  --z-index-dropdown: 1000;
  --z-index-popover: 1010;
  --z-index-tooltip: 1020;
  --z-index-modal-backdrop: 1030;
  --z-index-modal: 1040;
  --z-index-toast: 1050;

  /* Navigation */
  --z-index-navbar: 1060;
  --z-index-sidebar: 1070;

  /* Widgets */
  --z-index-chatbot: 9990;
  --z-index-floating-action: 9995;
  --z-index-overlay: 9999;
}

/* Z-index utilities */
.z-base { z-index: var(--z-index-base); }
.z-content { z-index: var(--z-index-content); }
.z-sticky { z-index: var(--z-index-sticky); }
.z-dropdown { z-index: var(--z-index-dropdown); }
.z-popover { z-index: var(--z-index-popover); }
.z-tooltip { z-index: var(--z-index-tooltip); }
.z-modal-backdrop { z-index: var(--z-index-modal-backdrop); }
.z-modal { z-index: var(--z-index-modal); }
.z-toast { z-index: var(--z-index-toast); }
.z-navbar { z-index: var(--z-index-navbar); }
.z-sidebar { z-index: var(--z-index-sidebar); }
.z-chatbot { z-index: var(--z-index-chatbot); }
.z-floating-action { z-index: var(--z-index-floating-action); }
.z-overlay { z-index: var(--z-index-overlay); }

/* Card System Improvements */
.card-interactive {
  @apply cursor-pointer transform transition-all duration-200 hover:scale-[1.02] hover:shadow-lg;
}

.card-interactive:active {
  @apply scale-[0.98];
}

/* Better responsive spacing */
.responsive-grid {
  @apply grid gap-4 sm:gap-6 lg:gap-8;
}

.responsive-grid-1 { @apply grid-cols-1; }
.responsive-grid-2 { @apply grid-cols-1 md:grid-cols-2; }
.responsive-grid-3 { @apply grid-cols-1 md:grid-cols-2 lg:grid-cols-3; }
.responsive-grid-4 { @apply grid-cols-1 md:grid-cols-2 lg:grid-cols-4; }

/* Card sizing */
.card-sm { @apply p-4 rounded-lg; }
.card-md { @apply p-6 rounded-xl; }
.card-lg { @apply p-8 rounded-2xl; }

/* Widget positioning improvements - Fixed positioning for scroll following */
.widget-floating {
  position: fixed !important;
  bottom: 1rem;
  right: 1rem;
  z-index: var(--z-index-floating-action);
}

/* Chat widget specific positioning - Always fixed and unified across all tabs */
.chatbot-widget {
  position: fixed !important;
  bottom: 1rem;
  right: 1rem;
  z-index: var(--z-index-chatbot);
  width: 4rem;
  height: 4rem;
}

/* Chat widget expanded state - Larger for better visibility */
.chatbot-widget.expanded {
  width: 28rem;
  height: 37.5rem;
  max-height: 80vh;
}

/* Floating action button positioning - Always fixed and unified */
.fab-widget {
  position: fixed !important;
  bottom: 1rem;
  right: 6rem; /* Positioned to the left of chat widget */
  z-index: var(--z-index-floating-action);
  width: 4rem;
  height: 4rem;
}

/* Mobile adjustments - Stack vertically and maintain fixed positioning */
@media (max-width: 768px) {
  .chatbot-widget {
    position: fixed !important;
    bottom: 1rem;
    right: 1rem;
    z-index: var(--z-index-chatbot);
  }

  .fab-widget {
    position: fixed !important;
    bottom: 6rem; /* Stack above chat widget */
    right: 1rem;
    z-index: var(--z-index-floating-action);
  }

  .chatbot-widget.expanded {
    position: fixed !important;
    left: 0.5rem;
    right: 0.5rem;
    bottom: 1rem;
    width: auto;
    max-height: 75vh;
    z-index: var(--z-index-chatbot);
  }
}

/* Widget collision prevention */
.widget-container {
  position: fixed;
  pointer-events: none;
  z-index: var(--z-index-chatbot);
}

.widget-container > * {
  pointer-events: auto;
}

/* Global widget positioning - Ensures widgets appear consistently across all tabs */
.global-widget-chat {
  position: fixed !important;
  bottom: 1rem !important;
  right: 1rem !important;
  z-index: var(--z-index-chatbot) !important;
}

.global-widget-fab {
  position: fixed !important;
  bottom: 1rem !important;
  right: 6rem !important;
  z-index: var(--z-index-floating-action) !important;
}

/* Override any tab-specific positioning */
.tab-chat .global-widget-chat,
.tab-community .global-widget-chat,
.tab-dashboard .global-widget-chat,
.tab-check .global-widget-chat,
.tab-knowledge .global-widget-chat,
.tab-profile .global-widget-chat {
  position: fixed !important;
  bottom: 1rem !important;
  right: 1rem !important;
}

.tab-chat .global-widget-fab,
.tab-community .global-widget-fab,
.tab-dashboard .global-widget-fab,
.tab-check .global-widget-fab,
.tab-knowledge .global-widget-fab,
.tab-profile .global-widget-fab {
  position: fixed !important;
  bottom: 1rem !important;
  right: 6rem !important;
}

/* Responsive widget stacking */
@media (max-width: 768px) {
  .widget-stack {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    position: fixed;
    bottom: 1rem;
    right: 1rem;
    z-index: var(--z-index-floating-action);
  }

  .widget-stack .chatbot-widget {
    position: relative;
    bottom: auto;
    left: auto;
    right: auto;
  }

  .widget-stack .fab-widget {
    position: relative;
    bottom: auto;
    right: auto;
  }
}

/* Better shadow system */
.shadow-card { box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06); }
.shadow-card-hover { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06); }
.shadow-card-active { box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05); }

/* Loading states */
.skeleton {
  @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded;
}

.skeleton-text {
  @apply skeleton h-4;
}

.skeleton-title {
  @apply skeleton h-6;
}

.skeleton-avatar {
  @apply skeleton w-12 h-12 rounded-full;
}

/* Focus states */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800;
}

/* Enhanced Animation improvements */
.animate-fade-in-up {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px) translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateZ(0);
  }
  to {
    opacity: 1;
    transform: scale(1) translateZ(0);
  }
}

/* Enhanced hover effects for cards */
.card-hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover-lift:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Ripple effect for buttons */
.btn-ripple {
  position: relative;
  overflow: hidden;
}

.btn-ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn-ripple:active::before {
  width: 300px;
  height: 300px;
}

/* Magnetic hover effect */
.magnetic-hover {
  transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

/* Floating animation */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Pulse glow effect */
@keyframes pulseGlow {
  0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
  50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6); }
}

.animate-pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

/* Mobile-first responsive utilities */
@media (max-width: 640px) {
  .responsive-stack {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .responsive-hide {
    display: none;
  }

  .card-mobile {
    padding: 1rem;
    margin: 0 0.5rem;
  }
}

/* Tablet adjustments */
@media (min-width: 641px) and (max-width: 1024px) {
  .tablet-grid-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .tablet-stack {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
}

/* Desktop optimizations */
@media (min-width: 1025px) {
  .desktop-grid-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .desktop-grid-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/* Dark mode improvements */
.dark-mode-card {
  background-color: rgb(31 41 55);
  border-color: rgb(55 65 81);
  color: white;
}

.dark-mode-card-hover:hover {
  background-color: rgb(55 65 81);
}

/* Widget stacking for mobile */
@media (max-width: 768px) {
  .widget-stack {
    position: relative;
    bottom: auto;
    right: auto;
    width: 100%;
    margin-bottom: 1rem;
  }
  
  .widget-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: var(--z-index-modal);
  }
}
