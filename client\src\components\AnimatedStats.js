import React, { useEffect, useRef, useState } from 'react';
import { gsap } from '../utils/gsap';
import { useCounterAnimation } from '../hooks/useGSAP';
import { useTheme } from '../context/ThemeContext';
import { Users, CheckCircle, AlertTriangle, TrendingUp } from 'lucide-react';
import { useFirestoreStats } from '../hooks/useFirestoreStats';

const AnimatedStats = () => {
  const { isDarkMode } = useTheme();
  const containerRef = useRef();
  const firestoreStats = useFirestoreStats();

  // State for real stats
  const [realStats, setRealStats] = useState({
    users: 0,
    checks: 0,
    accuracy: 0,
    threats: 0
  });

  // Update real stats when Firestore data loads
  useEffect(() => {
    if (!firestoreStats.loading && !firestoreStats.error) {
      setRealStats({
        users: firestoreStats.totalComments + firestoreStats.totalVotes + 50, // Estimate active users
        checks: firestoreStats.totalPosts,
        accuracy: firestoreStats.totalVotes > 0 ? Math.min(95, Math.max(85, Math.round((firestoreStats.totalVotes / firestoreStats.totalPosts) * 100))) : 94,
        threats: Math.round(firestoreStats.totalPosts * 0.15) // Estimate 15% threats detected
      });
    }
  }, [firestoreStats]);

  // Counter animations with dynamic values
  const [usersCountRef, startUsersCount] = useCounterAnimation(realStats.users || 1247, { duration: 2 });
  const [checksCountRef, startChecksCount] = useCounterAnimation(realStats.checks || 8934, { duration: 2.5 });
  const [accuracyRef, startAccuracy] = useCounterAnimation(realStats.accuracy || 94, { duration: 1.8 });
  const [trendsRef, startTrends] = useCounterAnimation(realStats.threats || 156, { duration: 2.2 });

  const stats = [
    {
      icon: Users,
      label: 'Người dùng hoạt động',
      value: realStats.users || 1247,
      suffix: '+',
      color: 'from-blue-500 to-blue-600',
      ref: usersCountRef,
      start: startUsersCount
    },
    {
      icon: CheckCircle,
      label: 'Bài viết đã kiểm tra',
      value: realStats.checks || 8934,
      suffix: '+',
      color: 'from-green-500 to-green-600',
      ref: checksCountRef,
      start: startChecksCount
    },
    {
      icon: TrendingUp,
      label: 'Độ chính xác',
      value: realStats.accuracy || 94,
      suffix: '%',
      color: 'from-purple-500 to-purple-600',
      ref: accuracyRef,
      start: startAccuracy
    },
    {
      icon: AlertTriangle,
      label: 'Tin giả phát hiện',
      value: realStats.threats || 156,
      suffix: '+',
      color: 'from-orange-500 to-orange-600',
      ref: trendsRef,
      start: startTrends
    }
  ];

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    console.log('🚀 AnimatedStats: Content visible immediately, animations on scroll...');

    // Set initial state - cards visible immediately but positioned for animation
    gsap.set(container.children, {
      opacity: 1, // Content visible immediately
      y: 30,
      scale: 0.95
    });

    // Create ScrollTrigger timeline for smooth animations
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: container,
        start: "top 85%",
        end: "bottom 20%",
        toggleActions: "play none none reverse",
        onEnter: () => {
          // Start counter animations when scrolled into view
          setTimeout(() => {
            startUsersCount();
            startChecksCount();
            startAccuracy();
            startTrends();
          }, 200);
        }
      }
    });

    // Smooth stagger animation on scroll
    tl.to(container.children, {
      y: 0,
      scale: 1,
      duration: 0.8,
      ease: "power2.out",
      stagger: 0.15
    });

    // Enhanced hover effects with better performance
    const cards = Array.from(container.children);
    const eventHandlers = new Map();

    cards.forEach((card) => {
      const handleMouseEnter = () => {
        gsap.to(card, {
          y: -8,
          scale: 1.02,
          duration: 0.3,
          ease: "power2.out"
        });
      };

      const handleMouseLeave = () => {
        gsap.to(card, {
          y: 0,
          scale: 1,
          duration: 0.3,
          ease: "power2.out"
        });
      };

      // Store handlers for cleanup
      eventHandlers.set(card, { handleMouseEnter, handleMouseLeave });

      card.addEventListener('mouseenter', handleMouseEnter);
      card.addEventListener('mouseleave', handleMouseLeave);
    });

    // Cleanup function
    return () => {
      tl.kill();
      cards.forEach((card) => {
        const handlers = eventHandlers.get(card);
        if (handlers) {
          card.removeEventListener('mouseenter', handlers.handleMouseEnter);
          card.removeEventListener('mouseleave', handlers.handleMouseLeave);
        }
      });
      eventHandlers.clear();
    };
  }, [startUsersCount, startChecksCount, startAccuracy, startTrends]);

  return (
    <div className={`py-16 ${isDarkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className={`text-3xl md:text-4xl font-bold mb-4 ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            Thống Kê Hoạt Động
          </h2>
          <p className={`text-lg ${
            isDarkMode ? 'text-gray-300' : 'text-gray-600'
          }`}>
            Cộng đồng FactCheck đang phát triển mạnh mẽ
          </p>
        </div>

        {/* Responsive Grid: 1 col mobile, 2 col tablet, 4 col desktop */}
        <div ref={containerRef} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat) => {
            const IconComponent = stat.icon;
            return (
              <div
                key={stat.label}
                className={`group relative p-6 rounded-2xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden border border-gray-200 dark:border-gray-600 ${
                  isDarkMode ? 'bg-gray-700' : 'bg-white'
                }`}
              >
                {/* Background gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-0 group-hover:opacity-10 transition-opacity duration-300`}></div>
                
                {/* Animated background circle */}
                <div className={`absolute -top-4 -right-4 w-20 h-20 bg-gradient-to-br ${stat.color} opacity-10 rounded-full group-hover:scale-150 transition-transform duration-500`}></div>
                
                <div className="relative z-10 text-center">
                  {/* Icon */}
                  <div className={`w-14 h-14 mx-auto mb-4 rounded-2xl bg-gradient-to-br ${stat.color} flex items-center justify-center text-white group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-md`}>
                    <IconComponent size={20} />
                  </div>
                  
                  {/* Counter */}
                  <div className={`text-3xl font-bold mb-2 ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    <span ref={stat.ref}>0</span>
                    <span className={`text-xl bg-gradient-to-r ${stat.color} bg-clip-text text-transparent`}>
                      {stat.suffix}
                    </span>
                  </div>
                  
                  {/* Label */}
                  <p className={`text-sm font-medium ${
                    isDarkMode ? 'text-gray-300' : 'text-gray-600'
                  }`}>
                    {stat.label}
                  </p>
                  
                  {/* Progress bar */}
                  <div className={`mt-4 w-full h-1 rounded-full overflow-hidden ${
                    isDarkMode ? 'bg-gray-600' : 'bg-gray-200'
                  }`}>
                    <div 
                      className={`h-full bg-gradient-to-r ${stat.color} rounded-full transform -translate-x-full group-hover:translate-x-0 transition-transform duration-1000 ease-out`}
                    ></div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default AnimatedStats;
