# 🚀 Render Quick Deploy - FactCheck Platform

## ⚡ **TL;DR - Deploy ngay trong 30 phút**

```bash
# 1. Tạo deployment files
npm run deploy:render

# 2. Chạy PowerShell helper (Windows)
./deploy-render.ps1

# 3. Deploy theo thứ tự trên Render Dashboard
```

## 📋 **Có cần điều chỉnh nhiều không?**

### ✅ **KHÔNG cần điều chỉnh nhiều:**

1. **Code đã sẵn sàng**: Tất cả services đã có health checks, CORS, environment handling
2. **Deployment files tự động**: Script tạo sẵn tất cả YAML configs
3. **Environment variables**: Đã standardize và document đầy đủ
4. **Build commands**: Đã optimize cho Render platform

### 🔧 **Chỉ cần điều chỉnh nhỏ:**

1. **Environment Variables**: Copy từ `.env` local sang Render
2. **Service URLs**: Update sau khi deploy từng service
3. **API Keys**: Add vào Render environment (optional)

## 🎯 **Deployment Strategy**

### **Option 1: <PERSON>orepo (Recommended)**
- ✅ Keep current repository structure
- ✅ Deploy each service separately on Render
- ✅ Use generated YAML configs

### **Option 2: Split Repositories**
- ❌ More complex
- ❌ Need to maintain multiple repos
- ❌ Not recommended

## 📊 **Services Overview**

| Priority | Service | Type | Port | Render Name |
|----------|---------|------|------|-------------|
| 1 | Auth Service | Web | 3001 | factcheck-auth |
| 2 | Link Service | Web | 3002 | factcheck-link |
| 2 | Community Service | Web | 3003 | factcheck-community |
| 2 | Chat Service | Web | 3004 | factcheck-chat |
| 3 | News Service | Web | 3005 | factcheck-news |
| 3 | Admin Service | Web | 3006 | factcheck-admin |
| 3 | PhishTank Service | Web | 3007 | factcheck-phishtank |
| 3 | CriminalIP Service | Web | 3008 | factcheck-criminalip |
| 4 | API Gateway | Web | 8080 | factcheck-api-gateway |
| 5 | Frontend | Static | 3000 | factcheck-frontend |

## 🔑 **Environment Variables Checklist**

### **Minimal Required (để chạy được):**
```bash
NODE_ENV=production
JWT_SECRET=(auto-generated by Render)
```

### **Firebase (cho auth, community, chat):**
```bash
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----..."
FIREBASE_CLIENT_EMAIL=<EMAIL>
```

### **API Keys (optional - có thì tốt):**
```bash
GEMINI_API_KEY=your-key
VIRUSTOTAL_API_KEY=your-key
SCAMADVISER_API_KEY=your-key
NEWSAPI_API_KEY=your-key
PHISHTANK_API_KEY=your-key
CRIMINALIP_API_KEY=your-key
```

## 🚀 **Step-by-Step Deployment**

### **Step 1: Prepare**
```bash
npm run deploy:render  # Tạo deployment files
```

### **Step 2: Deploy Services (theo thứ tự)**

1. **Auth Service** → https://factcheck-auth.onrender.com
2. **Link Service** → https://factcheck-link.onrender.com
3. **Community Service** → https://factcheck-community.onrender.com
4. **Chat Service** → https://factcheck-chat.onrender.com
5. **News Service** → https://factcheck-news.onrender.com
6. **Admin Service** → https://factcheck-admin.onrender.com
7. **PhishTank Service** → https://factcheck-phishtank.onrender.com
8. **CriminalIP Service** → https://factcheck-criminalip.onrender.com
9. **API Gateway** → https://factcheck-api-gateway.onrender.com
10. **Frontend** → https://factcheck-frontend.onrender.com

### **Step 3: Update Service URLs**

Sau khi deploy từng service, update environment variables:

```bash
# API Gateway environment
AUTH_SERVICE_URL=https://factcheck-auth.onrender.com
LINK_SERVICE_URL=https://factcheck-link.onrender.com
COMMUNITY_SERVICE_URL=https://factcheck-community.onrender.com
CHAT_SERVICE_URL=https://factcheck-chat.onrender.com
NEWS_SERVICE_URL=https://factcheck-news.onrender.com
ADMIN_SERVICE_URL=https://factcheck-admin.onrender.com
PHISHTANK_SERVICE_URL=https://factcheck-phishtank.onrender.com
CRIMINALIP_SERVICE_URL=https://factcheck-criminalip.onrender.com

# Frontend environment
REACT_APP_API_URL=https://factcheck-api-gateway.onrender.com
```

## ⚠️ **Render Free Tier Limitations**

1. **Sleep after 15 minutes** - Services hibernate when inactive
2. **Cold start delay** - First request after sleep takes 30-60 seconds
3. **512MB RAM limit** - Per service
4. **Build time limit** - 20 minutes max
5. **No persistent storage** - Use external databases

## 🎉 **Final Result**

Sau khi deploy xong:
- **Frontend**: https://factcheck-frontend.onrender.com
- **API**: https://factcheck-api-gateway.onrender.com
- **All services**: Running and connected

## 💡 **Pro Tips**

1. **Deploy Auth first** - Other services depend on it
2. **Test each service** - Check `/health` endpoint
3. **Update URLs incrementally** - Don't change all at once
4. **Monitor logs** - Render provides real-time logs
5. **Use environment groups** - For shared variables

## 🔧 **Troubleshooting**

### **Build Failures:**
- Check Node.js version (use 18.x)
- Verify package.json paths
- Check build logs for errors

### **Service Connection Issues:**
- Verify environment URLs
- Check CORS settings
- Test health endpoints

### **Frontend Issues:**
- Update REACT_APP_API_URL
- Check build output
- Verify static file serving

## 📚 **Documentation Files**

- `RENDER_DEPLOYMENT_GUIDE.md` - Detailed guide
- `docs/deployment/render-*.yaml` - Individual service configs
- `deploy-render.ps1` - PowerShell helper script
- `.env.example` - Environment template

**Kết luận: Deployment trên Render rất đơn giản, chỉ cần 30 phút và không cần điều chỉnh code nhiều!**
