apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: anti-fraud-platform
data:
  NODE_ENV: "production"
  AUTH_SERVICE_URL: "http://auth-service:3001"
  LINK_SERVICE_URL: "http://link-service:3002"
  COMMUNITY_SERVICE_URL: "http://community-service:3003"
  CHAT_SERVICE_URL: "http://chat-service:3004"
  NEWS_SERVICE_URL: "http://news-service:3005"
  ADMIN_SERVICE_URL: "http://admin-service:3006"
  REDIS_URL: "redis://redis:6379"
  
# Note: Secrets are created dynamically by the deploy script from .env file
# This avoids storing sensitive data in version control
