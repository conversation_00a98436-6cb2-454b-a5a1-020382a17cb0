# Production Environment Variables for Docker Deployment
# Sử dụng credentials thật từ file .env hiện tại

# =================================
# AUTH SERVICE - DOCKER DEPLOYMENT
# =================================
PORT=10000
NODE_ENV=production
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-factcheck-microservices-2024
FIREBASE_PROJECT_ID=factcheck-1d6e8
FIREBASE_CLIENT_EMAIL=<EMAIL>
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
FIREBASE_DATABASE_URL=https://factcheck-1d6e8-default-rtdb.firebaseio.com/

# =================================  
# API GATEWAY - DOCKER DEPLOYMENT
# =================================
PORT=10000
NODE_ENV=production
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-factcheck-microservices-2024
AUTH_SERVICE_URL=https://factcheck-auth-docker.onrender.com
LINK_SERVICE_URL=https://factcheck-link-docker.onrender.com
COMMUNITY_SERVICE_URL=https://factcheck-community-docker.onrender.com
CHAT_SERVICE_URL=https://factcheck-chat-docker.onrender.com
NEWS_SERVICE_URL=https://factcheck-news-docker.onrender.com
ADMIN_SERVICE_URL=https://factcheck-admin-docker.onrender.com
PHISHTANK_SERVICE_URL=https://factcheck-phishtank-docker.onrender.com
CRIMINALIP_SERVICE_URL=https://factcheck-criminalip-docker.onrender.com

# =================================
# LINK SERVICE - DOCKER DEPLOYMENT  
# =================================
PORT=10000
NODE_ENV=production
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-factcheck-microservices-2024
AUTH_SERVICE_URL=https://factcheck-auth-docker.onrender.com
VIRUSTOTAL_API_KEY=c4f8f7b8a8b9c8d9e8f9g8h9i8j9k8l9m8n9o8p9q8r9s8t9u8v9w8x9y8z9a8b9
SCAMADVISER_API_KEY=26a9e8085dmsh56a3ed6cf875fe7p15706jsn3244eb2976af
IPQUALITYSCORE_API_KEY=********************************

# =================================
# COMMUNITY SERVICE - DOCKER DEPLOYMENT
# =================================
PORT=10000
NODE_ENV=production
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-factcheck-microservices-2024
AUTH_SERVICE_URL=https://factcheck-auth-docker.onrender.com
FIREBASE_PROJECT_ID=factcheck-1d6e8
FIREBASE_CLIENT_EMAIL=<EMAIL>
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# =================================
# CHAT SERVICE - DOCKER DEPLOYMENT
# =================================
PORT=10000
NODE_ENV=production
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-factcheck-microservices-2024
AUTH_SERVICE_URL=https://factcheck-auth-docker.onrender.com
FIREBASE_PROJECT_ID=factcheck-1d6e8
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC9ThjEoNlmvc1G\n3KmKda7H9SgaT9BGSL8tMI6DPcBJGOOWPCGGWaIFCUFzrivVf1IMHp6evPZ5HxYW\nejOA4VjJIJJDsaeMoMMED0NiPAC1nGJfOWzMHoBBPvZccDPBdeZDR/kvi3aIupHy\nsF/9VLgjkfyBYGCxJzyCWOfNamuVp1pViE0MD6DY5aw1WTQvIfVtCgauZ0lFIGjQ\nibsSkN4wWOup49mq4nEuHYK26coTmDUTaiamrAFYynLrnNprvA2JgAoZISw2e30m\nYSsjEWENcXahS4bUrCYrOOyZHgvR1XeQ9eaCAo5elhvM9dfZfXaOgL5ZTVTR7GCp\nMc/hYbvhAgMBAAECggEACOZReN/RmkhgWXO/y7wQt++99cJPubIr" | clip
