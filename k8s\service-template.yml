# Template for microservice deployment
# Replace {{SERVICE_NAME}} and {{PORT}} with actual values

apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{SERVICE_NAME}}
  namespace: anti-fraud-platform
  labels:
    app: {{SERVICE_NAME}}
    version: v1
spec:
  replicas: 2
  selector:
    matchLabels:
      app: {{SERVICE_NAME}}
  template:
    metadata:
      labels:
        app: {{SERVICE_NAME}}
        version: v1
    spec:
      containers:
      - name: {{SERVICE_NAME}}
        image: ghcr.io/your-org/anti-fraud-platform/{{SERVICE_NAME}}:latest
        ports:
        - containerPort: {{PORT}}
        env:
        - name: PORT
          value: "{{PORT}}"
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: NODE_ENV
        envFrom:
        - configMapRef:
            name: app-config
        - secretRef:
            name: app-secrets
        livenessProbe:
          httpGet:
            path: /health
            port: {{PORT}}
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: {{PORT}}
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL

---
apiVersion: v1
kind: Service
metadata:
  name: {{SERVICE_NAME}}
  namespace: anti-fraud-platform
  labels:
    app: {{SERVICE_NAME}}
spec:
  selector:
    app: {{SERVICE_NAME}}
  ports:
  - name: http
    port: {{PORT}}
    targetPort: {{PORT}}
    protocol: TCP
  type: ClusterIP

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{SERVICE_NAME}}-hpa
  namespace: anti-fraud-platform
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{SERVICE_NAME}}
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
