version: '3.8'

# Development version with hot reload - no rebuild needed for code changes
services:
  # API Gateway
  api-gateway:
    build:
      context: ./services/api-gateway
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=development
      - AUTH_SERVICE_URL=http://auth-service:3001
      - LINK_SERVICE_URL=http://link-service:3002
      - COMMUNITY_SERVICE_URL=http://community-service:3003
      - CHAT_SERVICE_URL=http://chat-service:3004
      - NEWS_SERVICE_URL=http://news-service:3005
      - ADMIN_SERVICE_URL=http://admin-service:3006
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./shared:/app/shared
      - ./services/api-gateway/src:/app/src
      - ./services/api-gateway/package.json:/app/package.json
    depends_on:
      - redis
      - auth-service
      - link-service
      - community-service
      - chat-service
      - news-service
      - admin-service
    networks:
      - microservices-network
    command: npm run dev

  # User Authentication Service
  auth-service:
    build:
      context: ./services/auth-service
      dockerfile: Dockerfile.dev
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
      - JWT_SECRET=${JWT_SECRET}
    env_file:
      - .env
    volumes:
      - ./shared:/app/shared
      - ./services/auth-service/src:/app/src
      - ./services/auth-service/package.json:/app/package.json
    networks:
      - microservices-network
    command: npm run dev

  # Link Verification Service
  link-service:
    build:
      context: ./services/link-service
      dockerfile: Dockerfile.dev
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - PORT=3002
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
      - VIRUSTOTAL_API_KEY=${VIRUSTOTAL_API_KEY}
      - SCAMADVISER_API_KEY=${SCAMADVISER_API_KEY}
      - SCREENSHOTLAYER_API_KEY=${SCREENSHOTLAYER_API_KEY}
      - AUTH_SERVICE_URL=http://auth-service:3001
    env_file:
      - .env
    volumes:
      - ./shared:/app/shared
      - ./services/link-service/src:/app/src
      - ./services/link-service/package.json:/app/package.json
    depends_on:
      - auth-service
    networks:
      - microservices-network
    command: npm run dev

  # Community Service
  community-service:
    build:
      context: ./services/community-service
      dockerfile: Dockerfile.dev
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=development
      - PORT=3003
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
      - AUTH_SERVICE_URL=http://auth-service:3001
      - REDIS_URL=redis://redis:6379
    env_file:
      - .env
    volumes:
      - ./shared:/app/shared
      - ./services/community-service/src:/app/src
      - ./services/community-service/package.json:/app/package.json
    depends_on:
      - auth-service
      - redis
    networks:
      - microservices-network
    command: npm run dev

  # Chat/AI Service
  chat-service:
    build:
      context: ./services/chat-service
      dockerfile: Dockerfile.dev
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=development
      - PORT=3004
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - AUTH_SERVICE_URL=http://auth-service:3001
    env_file:
      - .env
    volumes:
      - ./shared:/app/shared
      - ./services/chat-service/src:/app/src
      - ./services/chat-service/package.json:/app/package.json
    depends_on:
      - auth-service
    networks:
      - microservices-network
    command: npm run dev

  # News/Content Service
  news-service:
    build:
      context: ./services/news-service
      dockerfile: Dockerfile.dev
    ports:
      - "3005:3005"
    environment:
      - NODE_ENV=development
      - PORT=3005
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
      - NEWSAPI_API_KEY=${NEWSAPI_API_KEY}
      - NEWSDATA_API_KEY=${NEWSDATA_API_KEY}
      - AUTH_SERVICE_URL=http://auth-service:3001
    env_file:
      - .env
    volumes:
      - ./shared:/app/shared
      - ./services/news-service/src:/app/src
      - ./services/news-service/package.json:/app/package.json
    depends_on:
      - auth-service
    networks:
      - microservices-network
    command: npm run dev

  # Admin Service
  admin-service:
    build:
      context: ./services/admin-service
      dockerfile: Dockerfile.dev
    ports:
      - "3006:3006"
    environment:
      - NODE_ENV=development
      - PORT=3006
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
      - AUTH_SERVICE_URL=http://auth-service:3001
      - COMMUNITY_SERVICE_URL=http://community-service:3003
      - LINK_SERVICE_URL=http://link-service:3002
    env_file:
      - .env
    volumes:
      - ./shared:/app/shared
      - ./services/admin-service/src:/app/src
      - ./services/admin-service/package.json:/app/package.json
    depends_on:
      - auth-service
      - community-service
      - link-service
    networks:
      - microservices-network
    command: npm run dev

  # PhishTank Service
  phishtank-service:
    build:
      context: ./services/phishtank-service
      dockerfile: Dockerfile.dev
    ports:
      - "3007:3007"
    environment:
      - NODE_ENV=development
      - PORT=3007
      - AUTH_SERVICE_URL=http://auth-service:3001
      - REDIS_URL=redis://redis:6379
    env_file:
      - .env
    volumes:
      - ./shared:/app/shared
      - ./services/phishtank-service/src:/app/src
      - ./services/phishtank-service/package.json:/app/package.json
      - ./services/phishtank-service/data:/app/data
    depends_on:
      - auth-service
      - redis
    networks:
      - microservices-network
    command: npm run dev

  # CriminalIP Service
  criminalip-service:
    build:
      context: ./services/criminalip-service
      dockerfile: Dockerfile.dev
    ports:
      - "3008:3008"
    environment:
      - NODE_ENV=development
      - PORT=3008
      - AUTH_SERVICE_URL=http://auth-service:3001
      - CRIMINALIP_API_KEY=${CRIMINALIP_API_KEY}
    env_file:
      - .env
    volumes:
      - ./shared:/app/shared
      - ./services/criminalip-service/src:/app/src
      - ./services/criminalip-service/package.json:/app/package.json
    depends_on:
      - auth-service
    networks:
      - microservices-network
    command: npm run dev

  # Redis for caching and message queuing
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
    networks:
      - microservices-network

  # React Frontend with hot reload
  frontend:
    build:
      context: ./client
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8080
      - REACT_APP_FIREBASE_API_KEY=${REACT_APP_FIREBASE_API_KEY}
      - REACT_APP_FIREBASE_AUTH_DOMAIN=${REACT_APP_FIREBASE_AUTH_DOMAIN}
      - REACT_APP_FIREBASE_PROJECT_ID=${REACT_APP_FIREBASE_PROJECT_ID}
      - CHOKIDAR_USEPOLLING=true
    env_file:
      - .env
    volumes:
      - ./client/src:/app/src
      - ./client/public:/app/public
      - ./client/package.json:/app/package.json
      - /app/node_modules
    depends_on:
      - api-gateway
    networks:
      - microservices-network
    command: npm start

volumes:
  redis-data:

networks:
  microservices-network:
    driver: bridge
