version: '3.8'
services:
  redis:
    image: redis:7-alpine
    ports: ["6379:6379"]
    command: redis-server --requirepass antifraud123
  
  api-gateway:
    build: ./services/api-gateway
    ports: ["8080:8080"]
    environment:
      - REDIS_HOST=redis
      - CIRCUIT_BREAKER_ENABLED=true
    depends_on: [redis]
  
  auth-service:
    build: ./services/auth-service
    ports: ["3001:3001"]
    environment:
      - REDIS_HOST=redis
      - EVENT_BUS_ENABLED=true
    depends_on: [redis]
  
  prometheus:
    image: prom/prometheus
    ports: ["9090:9090"]
  
  grafana:
    image: grafana/grafana
    ports: ["3001:3000"]
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin