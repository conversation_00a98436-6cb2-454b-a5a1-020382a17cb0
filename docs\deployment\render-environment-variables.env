# Environment Variables Template for Render Deployment
# Copy these variables to your Render service environment variables
# Use "Add from .env" feature in Render dashboard

# =================================
# ADMIN SERVICE ENVIRONMENT VARIABLES
# =================================
PORT=10000
NODE_ENV=production
JWT_SECRET=your_jwt_secret_here_generate_a_strong_secret
AUTH_SERVICE_URL=https://factcheck-auth.onrender.com

# Firebase Configuration
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_private_key_here\n-----END PRIVATE KEY-----"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# =================================
# AUTH SERVICE ENVIRONMENT VARIABLES  
# =================================
# PORT=10000
# NODE_ENV=production
# JWT_SECRET=your_jwt_secret_here_generate_a_strong_secret
# FIREBASE_PROJECT_ID=your_firebase_project_id
# FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_private_key_here\n-----END PRIVATE KEY-----"
# FIREBASE_CLIENT_EMAIL=<EMAIL>
# FIREBASE_DATABASE_URL=https://your-project-default-rtdb.firebaseio.com/
# REDIS_URL=redis://username:password@hostname:port

# =================================
# API GATEWAY ENVIRONMENT VARIABLES
# =================================
# PORT=10000
# NODE_ENV=production
# JWT_SECRET=your_jwt_secret_here_generate_a_strong_secret
# AUTH_SERVICE_URL=https://factcheck-auth.onrender.com
# LINK_SERVICE_URL=https://factcheck-link.onrender.com
# COMMUNITY_SERVICE_URL=https://factcheck-community.onrender.com
# CHAT_SERVICE_URL=https://factcheck-chat.onrender.com
# NEWS_SERVICE_URL=https://factcheck-news.onrender.com
# ADMIN_SERVICE_URL=https://factcheck-admin.onrender.com
# PHISHTANK_SERVICE_URL=https://factcheck-phishtank.onrender.com
# CRIMINALIP_SERVICE_URL=https://factcheck-criminalip.onrender.com

# =================================
# LINK SERVICE ENVIRONMENT VARIABLES
# =================================
# PORT=10000
# NODE_ENV=production
# JWT_SECRET=your_jwt_secret_here_generate_a_strong_secret
# AUTH_SERVICE_URL=https://factcheck-auth.onrender.com
# VIRUSTOTAL_API_KEY=your_virustotal_api_key
# SCAMADVISER_API_KEY=your_scamadviser_api_key
# IPQUALITYSCORE_API_KEY=your_ipqualityscore_api_key

# =================================
# COMMUNITY SERVICE ENVIRONMENT VARIABLES
# =================================
# PORT=10000
# NODE_ENV=production
# JWT_SECRET=your_jwt_secret_here_generate_a_strong_secret
# AUTH_SERVICE_URL=https://factcheck-auth.onrender.com
# FIREBASE_PROJECT_ID=your_firebase_project_id
# FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_private_key_here\n-----END PRIVATE KEY-----"
# FIREBASE_CLIENT_EMAIL=<EMAIL>

# =================================
# CHAT SERVICE ENVIRONMENT VARIABLES
# =================================
# PORT=10000
# NODE_ENV=production
# JWT_SECRET=your_jwt_secret_here_generate_a_strong_secret
# AUTH_SERVICE_URL=https://factcheck-auth.onrender.com
# FIREBASE_PROJECT_ID=your_firebase_project_id
# FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_private_key_here\n-----END PRIVATE KEY-----"
# FIREBASE_CLIENT_EMAIL=<EMAIL>
# GEMINI_API_KEY=your_gemini_api_key

# =================================
# NEWS SERVICE ENVIRONMENT VARIABLES
# =================================
# PORT=10000
# NODE_ENV=production
# JWT_SECRET=your_jwt_secret_here_generate_a_strong_secret
# AUTH_SERVICE_URL=https://factcheck-auth.onrender.com
# FIREBASE_PROJECT_ID=your_firebase_project_id
# FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_private_key_here\n-----END PRIVATE KEY-----"
# FIREBASE_CLIENT_EMAIL=<EMAIL>
# NEWSAPI_API_KEY=your_newsapi_key
# NEWSDATA_API_KEY=your_newsdata_key

# =================================
# PHISHTANK SERVICE ENVIRONMENT VARIABLES
# =================================
# PORT=10000
# NODE_ENV=production
# JWT_SECRET=your_jwt_secret_here_generate_a_strong_secret
# AUTH_SERVICE_URL=https://factcheck-auth.onrender.com
# PHISHTANK_API_KEY=your_phishtank_api_key

# =================================
# CRIMINALIP SERVICE ENVIRONMENT VARIABLES
# =================================
# PORT=10000
# NODE_ENV=production
# JWT_SECRET=your_jwt_secret_here_generate_a_strong_secret
# AUTH_SERVICE_URL=https://factcheck-auth.onrender.com
# CRIMINALIP_API_KEY=your_criminalip_api_key

# =================================
# FRONTEND ENVIRONMENT VARIABLES
# =================================
# REACT_APP_API_URL=https://factcheck-api-gateway.onrender.com
# REACT_APP_FIREBASE_API_KEY=your_firebase_api_key
# REACT_APP_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
# REACT_APP_FIREBASE_PROJECT_ID=your_firebase_project_id
# REACT_APP_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
# REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
# REACT_APP_FIREBASE_APP_ID=your_firebase_app_id
# GENERATE_SOURCEMAP=false

# =================================
# INSTRUCTIONS FOR USE
# =================================
# 1. Copy the relevant section for each service
# 2. Replace placeholder values with your actual credentials
# 3. In Render dashboard, go to Environment tab
# 4. Click "Add from .env" 
# 5. Paste the environment variables for that specific service
# 6. Render will automatically parse and add them
