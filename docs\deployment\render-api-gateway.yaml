# Render deployment configuration for factcheck-api-gateway
# Generated automatically - do not edit manually

services:
  - type: web
    name: factcheck-api-gateway
    env: node
    plan: free
    region: singapore
    rootDir: services/api-gateway
    buildCommand: npm install
    startCommand: npm start
    healthCheckPath: /health
    envVars:
      - key: PORT
        value: "10000"
      - key: NODE_ENV
        value: production
      - key: JWT_SECRET
        sync: false
      - key: AUTH_SERVICE_URL
        value: https://factcheck-auth.onrender.com
      - key: LINK_SERVICE_URL
        value: https://factcheck-link.onrender.com
      - key: COMMUNITY_SERVICE_URL
        value: https://factcheck-community.onrender.com
      - key: CHAT_SERVICE_URL
        value: https://factcheck-chat.onrender.com
      - key: NEWS_SERVICE_URL
        value: https://factcheck-news.onrender.com
      - key: ADMIN_SERVICE_URL
        value: https://factcheck-admin.onrender.com
      - key: PHISHTANK_SERVICE_URL
        value: https://factcheck-phishtank.onrender.com
      - key: CRIMINALIP_SERVICE_URL
        value: https://factcheck-criminalip.onrender.com